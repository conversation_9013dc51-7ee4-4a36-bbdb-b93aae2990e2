[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\clienthandler.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/clienthandler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\connectionpool.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/connectionpool.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\main.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\server.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/server.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\clienthandler.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/clienthandler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\connectionpool.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/connectionpool.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\server.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/server.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-nologo", "-Zc:wchar_t", "-FS", "-Zc:rvalueCast", "-Zc:inline", "-Zc:strictStrings", "-Zc:<PERSON><PERSON><PERSON>", "-permissive-", "-Zc:__cplusplus", "-Zc:externConstexpr", "-<PERSON>i", "-MDd", "-clang:-std=c++17", "-utf-8", "-W3", "-w34100", "-w34189", "-w44456", "-w44457", "-w44458", "-wd4577", "-wd4467", "-EHsc", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.44", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-DWIN64", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:E:\\YingYong\\VisualStudio\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MSVC2022_64bit-Debug\\ui_server.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MSVC2022_64bit-Debug/ui_server.h"}]