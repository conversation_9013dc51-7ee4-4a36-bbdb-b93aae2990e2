QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 13
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 1
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed \
    E:/YingYong/QT/Tools/mingw1310_64/x86_64-w64-mingw32/include
QMAKE_CXX.LIBDIRS = \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0 \
    E:/YingYong/QT/Tools/mingw1310_64/lib/gcc \
    E:/YingYong/QT/Tools/mingw1310_64/x86_64-w64-mingw32/lib \
    E:/YingYong/QT/Tools/mingw1310_64/lib
