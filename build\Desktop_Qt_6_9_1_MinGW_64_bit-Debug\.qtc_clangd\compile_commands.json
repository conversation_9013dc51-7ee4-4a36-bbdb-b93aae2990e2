[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\clienthandler.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/clienthandler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\connectionpool.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/connectionpool.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\main.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\server.cpp"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/server.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\clienthandler.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/clienthandler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\connectionpool.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/connectionpool.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\server.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/server.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\YingYong\\QT\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQuick", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtOpenGL", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtWidgets", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtGui", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlModels", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQml", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtNetwork", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtSql", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-IE:\\YingYong\\QT\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "E:\\YingYong\\QT\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "E:\\YingYong\\QT\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\Desktop_xiao\\100\\SULIAO-main\\server\\server\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\ui_server.h"], "directory": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/Desktop_xiao/100/SULIAO-main/server/server/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/ui_server.h"}]